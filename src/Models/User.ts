import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from "typeorm";
import { Branch } from "./Branch";
import { Attendance } from "./Attendance";
import { LeaveRequest } from "./LeaveRequest";

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: "text" })
  name!: string;

  @Column({ type: "text" })
  password!: string;

  @Column({ type: "text"})
  tel!: string;

  @Column({ type: "text" })
  role!: string;

  @Column({ type: "integer", default: 0 })
  hour_work!: number;

  @Column({ type: "integer", default: 0 })
  sick_leave!: number;

  @Column({ type: "integer", default: 0 })
  personal_leave!: number;

  @Column({ type: "text" })
  image!: string;

  @Column({ type: "text", nullable: true })
  day_off!: string;

  @Column({ type: "text", nullable: true })
  email!: string;

  @ManyToOne(() => Branch, { onDelete: "CASCADE" })
  @JoinColumn()
  branch!: Branch;

  @OneToMany(() => Attendance, attendance => attendance.user)
  attendances!: Attendance[];

  @OneToMany(() => LeaveRequest, leaveRequest => leaveRequest.user)
  leaveRequests!: LeaveRequest[];
}
