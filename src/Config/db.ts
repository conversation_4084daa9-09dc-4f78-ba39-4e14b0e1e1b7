import "reflect-metadata";
import { DataSource } from "typeorm";
import { SupplierType } from "../Models/SupplierType";
import { Supplier } from "../Models/Supplier";
import { ProductGroup } from "../Models/ProductGroup";
import { Product } from "../Models/Product";
import { Stock } from "../Models/Stock";
import { Branch } from "../Models/Branch";
import { User } from "../Models/User";
import { PurchaseOrder } from "../Models/PurchaseOrder";
import { PurchaseOrderItem } from "../Models/PurchaseOrderItem";
import { SpecialReportGroup } from "../Models/SpecialReportGroup";
import { Attendance } from "../Models/Attendance";
import { LeaveRequest } from "../Models/LeaveRequest";

export const AppDataSource = new DataSource({
  type: "sqlite",
  database: "./database2.sqlite",
  synchronize: false, // ตั้งค่าเป็น false ก่อน
  logging: false,
  entities: [
    SupplierType,
    Supplier,
    ProductGroup,
    Product,
    Stock,
    Branch,
    User,
    PurchaseOrder,
    PurchaseOrderItem,
    SpecialReportGroup,
    Attendance,
    LeaveRequest,
  ],
});

AppDataSource.initialize()
  .then(async () => {
    console.log("✅ Connected to SQLite database");

    // // ลบตาราง supplier หากอยู่
    // await AppDataSource.query(`DROP TABLE IF EXISTS supplier;`);
    // await AppDataSource.query(`DROP TABLE IF EXISTS supplierType;`); // ลบ SupplierType หากจำเป็น

    // รันการซิงโครไนซ์เพื่อสร้างตารางใหม่
    await AppDataSource.synchronize();
  })
  .catch((error) => {
    console.error("❌ Error connecting to SQLite:", error);
  });
